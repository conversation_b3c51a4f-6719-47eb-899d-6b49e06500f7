<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="支付宝配置" name="alipay">
        <el-form ref="alipayForm" :model="alipayConfig" :rules="alipayRules" label-width="150px">
          <el-form-item label="应用ID" prop="appId">
            <el-input v-model="alipayConfig.appId" placeholder="请输入支付宝应用ID" />
          </el-form-item>
          
          <el-form-item label="认证模式" prop="useCert">
            <el-radio-group v-model="alipayConfig.useCert">
              <el-radio :label="false">公钥模式</el-radio>
              <el-radio :label="true">证书模式</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <div v-if="!alipayConfig.useCert">
            <el-form-item label="商户私钥" prop="privateKey">
              <el-input v-model="alipayConfig.privateKey" type="textarea" :rows="4" placeholder="请输入商户私钥" />
            </el-form-item>
            
            <el-form-item label="支付宝公钥" prop="publicKey">
              <el-input v-model="alipayConfig.publicKey" type="textarea" :rows="4" placeholder="请输入支付宝公钥" />
            </el-form-item>
          </div>
          
          <div v-else>
            <el-form-item label="商户私钥" prop="privateKey">
              <el-input v-model="alipayConfig.privateKey" type="textarea" :rows="4" placeholder="请输入商户私钥" />
            </el-form-item>
            
            <el-form-item label="应用公钥证书路径" prop="appCertPath">
              <el-input v-model="alipayConfig.appCertPath" placeholder="请输入应用公钥证书路径">
                <el-button slot="append" @click="uploadCert('app')">上传证书</el-button>
              </el-input>
            </el-form-item>
            
            <el-form-item label="支付宝公钥证书路径" prop="alipayCertPath">
              <el-input v-model="alipayConfig.alipayCertPath" placeholder="请输入支付宝公钥证书路径">
                <el-button slot="append" @click="uploadCert('alipay')">上传证书</el-button>
              </el-input>
            </el-form-item>
            
            <el-form-item label="支付宝根证书路径" prop="alipayRootCertPath">
              <el-input v-model="alipayConfig.alipayRootCertPath" placeholder="请输入支付宝根证书路径">
                <el-button slot="append" @click="uploadCert('root')">上传证书</el-button>
              </el-input>
            </el-form-item>
          </div>
          
          <el-form-item label="支付宝网关" prop="gatewayUrl">
            <el-select v-model="alipayConfig.gatewayUrl" placeholder="请选择支付宝网关">
              <el-option label="正式环境" value="https://openapi.alipay.com/gateway.do"></el-option>
              <el-option label="沙箱环境" value="https://openapi.alipaydev.com/gateway.do"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="异步通知地址" prop="notifyUrl">
            <el-input v-model="alipayConfig.notifyUrl" placeholder="请输入异步通知地址" />
          </el-form-item>
          
          <el-form-item label="同步跳转地址" prop="returnUrl">
            <el-input v-model="alipayConfig.returnUrl" placeholder="请输入同步跳转地址" />
          </el-form-item>
          
          <el-form-item label="签名方式" prop="signType">
            <el-select v-model="alipayConfig.signType" placeholder="请选择签名方式">
              <el-option label="RSA2" value="RSA2"></el-option>
              <el-option label="RSA" value="RSA"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="字符编码" prop="charset">
            <el-select v-model="alipayConfig.charset" placeholder="请选择字符编码">
              <el-option label="UTF-8" value="UTF-8"></el-option>
              <el-option label="GBK" value="GBK"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveAlipayConfig">保存配置</el-button>
            <el-button @click="testAlipayConfig">测试连接</el-button>
            <el-button @click="resetAlipayConfig">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="微信支付配置" name="wechat">
        <el-form ref="wechatForm" :model="wechatConfig" :rules="wechatRules" label-width="150px">
          <el-form-item label="应用ID" prop="appId">
            <el-input v-model="wechatConfig.appId" placeholder="请输入微信应用ID" />
          </el-form-item>
          
          <el-form-item label="商户号" prop="mchId">
            <el-input v-model="wechatConfig.mchId" placeholder="请输入微信商户号" />
          </el-form-item>
          
          <el-form-item label="商户密钥" prop="mchKey">
            <el-input v-model="wechatConfig.mchKey" type="textarea" :rows="4" placeholder="请输入商户密钥" />
          </el-form-item>
          
          <el-form-item label="证书路径" prop="certPath">
            <el-input v-model="wechatConfig.certPath" placeholder="请输入证书路径">
              <el-button slot="append" @click="uploadWechatCert">上传证书</el-button>
            </el-input>
          </el-form-item>
          
          <el-form-item label="异步通知地址" prop="notifyUrl">
            <el-input v-model="wechatConfig.notifyUrl" placeholder="请输入异步通知地址" />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveWechatConfig">保存配置</el-button>
            <el-button @click="testWechatConfig">测试连接</el-button>
            <el-button @click="resetWechatConfig">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="系统配置" name="system">
        <el-form ref="systemForm" :model="systemConfig" :rules="systemRules" label-width="150px">
          <el-form-item label="默认支付方式" prop="defaultPayType">
            <el-select v-model="systemConfig.defaultPayType" placeholder="请选择默认支付方式">
              <el-option label="支付宝" value="1"></el-option>
              <el-option label="微信支付" value="2"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="支付超时时间" prop="payTimeout">
            <el-input-number v-model="systemConfig.payTimeout" :min="1" :max="1440" /> 分钟
          </el-form-item>
          
          <el-form-item label="自动关闭订单" prop="autoClose">
            <el-switch v-model="systemConfig.autoClose" />
          </el-form-item>
          
          <el-form-item label="支付回调重试次数" prop="callbackRetry">
            <el-input-number v-model="systemConfig.callbackRetry" :min="0" :max="10" />
          </el-form-item>
          
          <el-form-item label="启用支付日志" prop="enableLog">
            <el-switch v-model="systemConfig.enableLog" />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveSystemConfig">保存配置</el-button>
            <el-button @click="resetSystemConfig">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 证书上传对话框 -->
    <el-dialog title="上传证书" :visible.sync="uploadDialogVisible" width="500px">
      <el-upload
        class="upload-demo"
        drag
        :action="uploadAction"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        accept=".crt,.pem,.p12"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传 .crt/.pem/.p12 文件</div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "PaymentConfig",
  data() {
    return {
      activeTab: 'alipay',
      uploadDialogVisible: false,
      uploadType: '',
      uploadAction: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      
      // 支付宝配置
      alipayConfig: {
        appId: '',
        useCert: false,
        privateKey: '',
        publicKey: '',
        appCertPath: '',
        alipayCertPath: '',
        alipayRootCertPath: '',
        gatewayUrl: 'https://openapi.alipaydev.com/gateway.do',
        notifyUrl: '',
        returnUrl: '',
        signType: 'RSA2',
        charset: 'UTF-8'
      },
      alipayRules: {
        appId: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
        privateKey: [{ required: true, message: '请输入商户私钥', trigger: 'blur' }],
        gatewayUrl: [{ required: true, message: '请选择支付宝网关', trigger: 'change' }],
        notifyUrl: [{ required: true, message: '请输入异步通知地址', trigger: 'blur' }]
      },
      
      // 微信支付配置
      wechatConfig: {
        appId: '',
        mchId: '',
        mchKey: '',
        certPath: '',
        notifyUrl: ''
      },
      wechatRules: {
        appId: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
        mchId: [{ required: true, message: '请输入商户号', trigger: 'blur' }],
        mchKey: [{ required: true, message: '请输入商户密钥', trigger: 'blur' }]
      },
      
      // 系统配置
      systemConfig: {
        defaultPayType: '1',
        payTimeout: 30,
        autoClose: true,
        callbackRetry: 3,
        enableLog: true
      },
      systemRules: {
        defaultPayType: [{ required: true, message: '请选择默认支付方式', trigger: 'change' }],
        payTimeout: [{ required: true, message: '请输入支付超时时间', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.loadConfigs();
  },
  methods: {
    handleTabClick(tab) {
      console.log('切换到标签页：', tab.name);
    },
    
    loadConfigs() {
      // 加载配置数据
      // 这里应该调用后端API获取配置
      console.log('加载配置数据');
    },
    
    saveAlipayConfig() {
      this.$refs.alipayForm.validate((valid) => {
        if (valid) {
          // 保存支付宝配置
          console.log('保存支付宝配置：', this.alipayConfig);
          this.$modal.msgSuccess("支付宝配置保存成功");
        }
      });
    },
    
    testAlipayConfig() {
      // 测试支付宝连接
      console.log('测试支付宝连接');
      this.$modal.msgSuccess("支付宝连接测试成功");
    },
    
    resetAlipayConfig() {
      this.$refs.alipayForm.resetFields();
    },
    
    saveWechatConfig() {
      this.$refs.wechatForm.validate((valid) => {
        if (valid) {
          // 保存微信支付配置
          console.log('保存微信支付配置：', this.wechatConfig);
          this.$modal.msgSuccess("微信支付配置保存成功");
        }
      });
    },
    
    testWechatConfig() {
      // 测试微信支付连接
      console.log('测试微信支付连接');
      this.$modal.msgSuccess("微信支付连接测试成功");
    },
    
    resetWechatConfig() {
      this.$refs.wechatForm.resetFields();
    },
    
    saveSystemConfig() {
      this.$refs.systemForm.validate((valid) => {
        if (valid) {
          // 保存系统配置
          console.log('保存系统配置：', this.systemConfig);
          this.$modal.msgSuccess("系统配置保存成功");
        }
      });
    },
    
    resetSystemConfig() {
      this.$refs.systemForm.resetFields();
    },
    
    uploadCert(type) {
      this.uploadType = type;
      this.uploadDialogVisible = true;
    },
    
    uploadWechatCert() {
      this.uploadType = 'wechat';
      this.uploadDialogVisible = true;
    },
    
    beforeUpload(file) {
      const isValidType = ['crt', 'pem', 'p12'].some(ext => file.name.toLowerCase().endsWith(ext));
      if (!isValidType) {
        this.$modal.msgError('只能上传 .crt/.pem/.p12 格式的证书文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('证书文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    
    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        const filePath = response.url;
        if (this.uploadType === 'app') {
          this.alipayConfig.appCertPath = filePath;
        } else if (this.uploadType === 'alipay') {
          this.alipayConfig.alipayCertPath = filePath;
        } else if (this.uploadType === 'root') {
          this.alipayConfig.alipayRootCertPath = filePath;
        } else if (this.uploadType === 'wechat') {
          this.wechatConfig.certPath = filePath;
        }
        this.$modal.msgSuccess('证书上传成功');
        this.uploadDialogVisible = false;
      } else {
        this.$modal.msgError('证书上传失败：' + response.msg);
      }
    },
    
    handleUploadError() {
      this.$modal.msgError('证书上传失败');
    }
  }
};
</script>

<style scoped>
.upload-demo {
  text-align: center;
}
</style>
